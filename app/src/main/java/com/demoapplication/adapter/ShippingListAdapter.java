package com.demoapplication.adapter;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.demoapplication.Modal.Shipping.GetOrder.GetShippingOrderInfoResponse;
import com.demoapplication.R;

import java.util.ArrayList;

public class ShippingListAdapter extends RecyclerView.Adapter<ShippingListAdapter.ViewHolder> {
    private static final String TAG = "ShippingListAdapter";
    private ArrayList<GetShippingOrderInfoResponse.ProductData> items;
    private onClickCallback onClick;
    private Context context;
    public int mSelectedId;

    public interface onClickCallback {
        void onClick(int position);
    }

    // Constructor for initialization
    public ShippingListAdapter(Context context, ArrayList<GetShippingOrderInfoResponse.ProductData> items, onClickCallback onclick) {
        this.context = context;
        this.items = items;
        this.onClick = onclick;
        Log.d(TAG, "Constructor: items size = " + (items != null ? items.size() : "null"));
    }

    @NonNull
    @Override
    public ShippingListAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // Inflating the Layout
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.adapter_shipping, parent, false);
        return new ViewHolder(view);
    }

    // Binding data to the into specified position
    @Override
    public void onBindViewHolder(@NonNull ShippingListAdapter.ViewHolder holder, int position) {
        Log.d(TAG, "onBindViewHolder: position = " + position);

        if (items == null || items.isEmpty() || position >= items.size()) {
            Log.e(TAG, "Invalid data or position: items = " + (items == null ? "null" : items.size()) + ", position = " + position);
            return;
        }

        GetShippingOrderInfoResponse.ProductData item = items.get(position);
        if (item == null) {
            Log.e(TAG, "Item at position " + position + " is null");
            return;
        }

        // Debug log to check actual values
        Log.d(TAG, "Item at position " + position + ": " +
                "productId=" + item.getProductId() +
                ", barcode=" + (item.getBarcode() != null ? item.getBarcode() : "null") +
                ", allocatedQty=" + item.getAllocatedQty() +
                ", scanedQty=" + item.getScanedQty() +
                ", lotNumber=" + item.getLotNumber() +
                ", expirationDate=" + item.getExpirationDate() +
                ", locationName=" + item.getLocationName());

        // Set barcode text
        if (item.getBarcode() != null && !item.getBarcode().isEmpty()) {
            String barcodeText = item.getBarcode().get(0);
            holder.text.setText(barcodeText != null ? barcodeText : "N/A");
        } else {
            holder.text.setText("N/A");
        }

        // Set allocated quantity
        Integer allocatedQty = item.getAllocatedQty();
        holder.text2.setText(allocatedQty != null ? allocatedQty.toString() : "0");

        // Ưu tiên hiển thị scanedQty nếu có
        Integer scanedQty = item.getScanedQty();
        if (scanedQty != null && scanedQty > 0) {
            holder.text3.setText(scanedQty.toString());
            Log.d(TAG, "Using scanedQty for display: " + scanedQty);
        } else {
            // Tính totalSplitQty như bình thường nếu không có scanedQty
            int totalSplitQty = 0;

            // Get information of the current product
            int currentProductId = item.getProductId();
            String currentLotNumber = item.getLotNumber() != null ? item.getLotNumber() : "";
            String currentExpirationDate = item.getExpirationDate() != null ? item.getExpirationDate() : "";
            String currentLocationName = item.getLocationName() != null ? item.getLocationName() : "";

            // Iterate through all products to find those with the same information
            for (GetShippingOrderInfoResponse.ProductData product : items) {
                if (product.getProductId() == currentProductId) {
                    String productLotNumber = product.getLotNumber() != null ? product.getLotNumber() : "";
                    String productExpirationDate = product.getExpirationDate() != null ? product.getExpirationDate() : "";
                    String productLocationName = product.getLocationName() != null ? product.getLocationName() : "";

                    // Check if the fields match
                    if (productLotNumber.equals(currentLotNumber) &&
                            productExpirationDate.equals(currentExpirationDate) &&
                            productLocationName.equals(currentLocationName)) {

                        //Calculate the total split_qty from split_qty_detail
                        if (product.getSplitQtyDetail() != null) {
                            for (GetShippingOrderInfoResponse.SplitQtyDetail detail : product.getSplitQtyDetail()) {
                                String detailLocation = detail.getLocationName() != null ? detail.getLocationName() : "";
                                String detailLot = detail.getLotNumber() != null ? detail.getLotNumber() : "";
                                String detailExpiration = detail.getExpirationDate() != null ? detail.getExpirationDate() : "";

                                if (detailLocation.equals(currentLocationName) &&
                                        detailLot.equals(currentLotNumber) &&
                                        detailExpiration.equals(currentExpirationDate)) {

                                    Integer splitQty = detail.getSplitQty();
                                    if (splitQty != null) {
                                        totalSplitQty += splitQty;
                                        Log.d(TAG, "Adding split_qty: " + splitQty +
                                                " for product " + currentProductId +
                                                " at location " + currentLocationName);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Set totalQty = totalSplitQty
            holder.text3.setText(String.valueOf(totalSplitQty));

            Log.d(TAG, "Final calculation for product " + currentProductId +
                    " at location " + currentLocationName +
                    ": allocatedQty=" + allocatedQty +
                    ", totalSplitQty=" + totalSplitQty);
        }

        // Điều chỉnh màu nền dựa trên scanedQty hoặc totalSplitQty
        try {
            int allocatedQtyValue = allocatedQty != null ? allocatedQty : 0;
            int displayedQty = scanedQty != null && scanedQty > 0 ? scanedQty : 0;

            Log.d(TAG, "Comparing allocatedQty: " + allocatedQtyValue + " with displayedQty: " + displayedQty);

            if (allocatedQtyValue > 0 && allocatedQtyValue <= displayedQty) {
                holder.ll.setBackgroundColor(Color.LTGRAY);
                Log.d(TAG, "Setting row to gray: allocatedQty <= displayedQty");
            } else {
                holder.ll.setBackgroundColor(Color.parseColor("#ffffff"));
            }
        } catch (NumberFormatException e) {
            Log.e(TAG, "Error parsing quantities: " + e.getMessage());
            holder.ll.setBackgroundColor(Color.parseColor("#ffffff"));
        }

        // Set click listener
        holder.ll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int adapterPosition = holder.getAdapterPosition();
                if (adapterPosition != RecyclerView.NO_POSITION) {
                    Log.d(TAG, "Item clicked at position: " + adapterPosition);
                    onClick.onClick(adapterPosition);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return items != null ? items.size() : 0;
    }

    // Update data in adapter
    public void updateData(ArrayList<GetShippingOrderInfoResponse.ProductData> newItems) {
        this.items = newItems;
        notifyDataSetChanged();
        Log.d(TAG, "Data updated: items size = " + (newItems != null ? newItems.size() : "null"));
    }

    // Initializing the Views
    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView text;
        TextView text2;
        TextView text3;
        LinearLayout ll;

        public ViewHolder(View view) {
            super(view);
            text = view.findViewById(R.id.inspection_txt1);
            text2 = view.findViewById(R.id.inspection_txt2);
            text3 = view.findViewById(R.id.inspection_txt3);
            ll = view.findViewById(R.id.workPickingHeaderl);
        }
    }
}
