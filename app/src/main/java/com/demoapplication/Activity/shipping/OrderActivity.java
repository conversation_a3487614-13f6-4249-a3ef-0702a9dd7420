package com.demoapplication.Activity.shipping;

import static com.demoapplication.ServerRetro.ApiUtils.makeApiCall;

import androidx.annotation.NonNull;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.util.TypedValue;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.demoapplication.Activity.BaseActivity;
import com.demoapplication.Modal.Login.LoginSetting;
import com.demoapplication.Modal.Shipping.GetOrder.GetShippingOrderInfoResponse;
import com.demoapplication.Modal.Shipping.ReShip.ReshipOrderRequest;
import com.demoapplication.Modal.Shipping.ReShip.ReshipOrderResponse;
import com.demoapplication.R;
import com.demoapplication.ServerRetro.CommonUtilities;
import com.demoapplication.ServerRetro.DataManager;
import com.demoapplication.ServerRetro.progresBar;
import com.demoapplication.Util.CustomPagerAdapter;
import com.demoapplication.Util.TextToSpeak;
import com.demoapplication.Util.U;
import com.google.android.material.navigation.NavigationView;
import com.google.gson.JsonIOException;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;

import cn.pedant.SweetAlert.SweetAlertDialog;
import okhttp3.ResponseBody;
import retrofit2.Call;

public class OrderActivity extends BaseActivity implements View.OnClickListener,DataManager.GetOrderInfoCall,DataManager.ReshipOrderInfoCall{
    private Button numbrbtn;
    private boolean showKeyboard;
    public Context mcontext = this;

    private TextToSpeak mTextToSpeak;
    public static final int PROC_ORDER_ID = 1;
    public static final int PROC_TRACK = 2;
    protected int mProcNo = 0;
    private EditText orderId, trackingNum;
    TextView totaltrack;
    RelativeLayout mainlayout;
    protected RelativeLayout layout;

    DataManager.ReshipOrderInfoCall reshipOrderInfoCall;
    DataManager.GetOrderInfoCall getOrderInfoCall;

    progresBar progress;
    DataManager manager;

    public static ArrayList<GetShippingOrderInfoResponse.ProductData> mProductList;
    public static ArrayList<GetShippingOrderInfoResponse.PackagesData> mPackageList;
    ArrayList<String> tracklist = new ArrayList<>();
    String remark = "";

    private boolean visible = false;
    int count=0;
    private String TAG = OrderActivity.class.getSimpleName();
    public TextView userName, userId;
    LinearLayout orderIdLayout, trackingLayout;
    SweetAlertDialog reshipdialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_order);
        mTextToSpeak = new TextToSpeak(this);
        ImageView imagemenu = findViewById(R.id.menuIV);
        DrawerLayout drawerLayout = findViewById(R.id.drawer_layout1);
        NavigationView navigationView = findViewById(R.id.navigation_view);
        View header = navigationView.getHeaderView(0);
        header.findViewById(R.id.menuIV1);
        navigationView.setBackgroundColor(getResources().getColor(R.color.ligt_skybg));
        userName = header.findViewById(R.id.txtName1);
        userId = header.findViewById(R.id.idName1);


        String user = LoginSetting.getUserName(OrderActivity.this);
        userName.setText(" : " + user);
        int shopID = BaseActivity.getShopId();
        userId.setText(" : " + shopID);
        progress = new progresBar(this);
        manager = new DataManager(this);
        getOrderInfoCall = this;
        reshipOrderInfoCall = this;

        imagemenu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                drawerLayout.openDrawer(GravityCompat.START);

            }
        });

        header.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                drawerLayout.closeDrawer(GravityCompat.START);

            }
        });


        navigationView.setNavigationItemSelectedListener(new NavigationView.OnNavigationItemSelectedListener() {
            @Override
            public boolean onNavigationItemSelected(@NonNull MenuItem item) {
                NavigationDrawer(item,R.id. nav_account, OrderActivity.this);
                drawerLayout.closeDrawer(GravityCompat. START ) ;
                return true;
            }
        });


        numbrbtn = findViewById(R.id.add_layout);
        mainlayout = findViewById(R.id.layout_main);
        orderId = findViewById(R.id.orderId);
        trackingNum = findViewById(R.id.trackingNumber);
        totaltrack = findViewById(R.id.totaltrack);
        layout = findViewById(R.id.layout_number);
        ViewPager viewPager = (ViewPager) findViewById(R.id.viewpager);
        viewPager.setAdapter(new CustomPagerAdapter(this));

        // Initialize views
        orderIdLayout = findViewById(R.id.orderIdLayout);
        trackingLayout = findViewById(R.id.trackingLayout);

        if (BaseActivity.getShippingStartKey() == 0) {
            orderIdLayout.setVisibility(View.VISIBLE);
            if (BaseActivity.getNotInspectTrackingNo() == 0) {
                trackingLayout.setVisibility(View.VISIBLE);
            } else {
                trackingLayout.setVisibility(View.GONE);
            }
            setProc(PROC_ORDER_ID);
        } else {
            orderIdLayout.setVisibility(View.GONE);
            if (BaseActivity.getNotInspectTrackingNo() == 0) {
                trackingLayout.setVisibility(View.VISIBLE);
                setProc(PROC_TRACK);
            } else {
                trackingLayout.setVisibility(View.GONE);
                U.beepError(this, "(HMS Parameter error) Tracking number is not show");
            }
        }

        showKeyboard= true;

        if (showKeyboard == true) {
            ViewGroup hiddenPanel = (ViewGroup) findViewById((R.id.layout_number));
            visible = true;
            numbrbtn.setText(R.string.hideKeyboard);
            mainlayout = (RelativeLayout) findViewById(R.id.layout_main);
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) mainlayout.getLayoutParams();
            int bottom = convert(200);
            int top = convert(10);
            params.setMargins(0, 0, 0, bottom);
            layout = (RelativeLayout) findViewById(R.id.layout_number);
            RelativeLayout.LayoutParams param = (RelativeLayout.LayoutParams) layout.getLayoutParams();
            param.setMargins(0, top, 0, 0);

            mainlayout.setLayoutParams(params);
            Animation bottomUp = AnimationUtils.loadAnimation(this, R.anim.bottom_up);

            hiddenPanel.startAnimation(bottomUp);
            hiddenPanel.setVisibility(View.VISIBLE);
            numbrbtn.setVisibility(View.GONE);
        }

        if (mProcNo == 0){
            nextProcess();
        }

    }

    public void GotoNext() {
        Intent i =  new Intent(OrderActivity.this, ShippingActivity.class);
        i.putExtra("order_id",orderId.getText().toString());
        i.putExtra("remark",remark);
        startActivity(i);
    }


    public void AddLayout(View view) {
        ViewGroup hiddenPanel = (ViewGroup) findViewById((R.id.layout_number));
        if (visible == false) {

            visible = true;
            numbrbtn.setText(R.string.hideKeyboard);
            mainlayout = (RelativeLayout) findViewById(R.id.layout_main);
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) mainlayout.getLayoutParams();
            int bottom = convert(200);
            int top = convert(10);
            params.setMargins(0, 0, 0, bottom);
            layout = (RelativeLayout) findViewById(R.id.layout_number);
            RelativeLayout.LayoutParams param = (RelativeLayout.LayoutParams) layout.getLayoutParams();
            param.setMargins(0, top, 0, 0);

            mainlayout.setLayoutParams(params);
            Animation bottomUp = AnimationUtils.loadAnimation(this, R.anim.bottom_up);

            hiddenPanel.startAnimation(bottomUp);
            hiddenPanel.setVisibility(View.VISIBLE);

        } else {
            visible = false;
            numbrbtn.setText(R.string.showkeyboard);
            mainlayout = (RelativeLayout) findViewById(R.id.layout_main);
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) mainlayout.getLayoutParams();
            params.setMargins(0, 0, 0, 10);
            Animation bottomDown = AnimationUtils.loadAnimation(this,
                    R.anim.bottom_down);

            hiddenPanel.startAnimation(bottomDown);
            hiddenPanel.setVisibility(View.INVISIBLE);

            mainlayout.setLayoutParams(params);
        }
    }
    @Override
    public void onBackPressed() {
        // TODO not backed from picking activity
        //super.onBackPressed();
    }
    public int convert(int x) {
        Resources r = mcontext.getResources();
        int px = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                x, r.getDisplayMetrics()
        );
        return px;
    }
    @Override
    public void inputedEvent(String buff) {
        switch (mProcNo) {
            case PROC_ORDER_ID:	// ?????
                String order = orderId.getText().toString();

                //todo: shipping
                if (order.isEmpty()) {
                    U.beepError(this, "注文IDは必須です");
                    orderId.setFocusableInTouchMode(true);
                    break;
                } else {
                    if (!CommonUtilities.getConnectivityStatus(this))
                        CommonUtilities.openInternetDialog(this);
                    else {
                        Log.e(TAG, "order request>>>>>>>>>>>>");

                        String orderNo;
                        if (order.matches("\\d+")) {
                            orderNo = "";
                        } else {
                            orderNo = order;
                        }

                        progress.Show();

                        Call<GetShippingOrderInfoResponse> apiCall = manager.GetShippingOrderInfo(
                                BaseActivity.getShopId(),
                                order,
                                BaseActivity.get_DeviceID(),
                                orderNo,
                                BaseActivity.getlotShipmentSelected() ? 1 : 0
                        );
                        makeApiCall(apiCall, this::onSucess, this::onError,this::onFaliure,this::onNetworkFailure);
                    }
                }
                break;
            case PROC_TRACK:

                String track = trackingNum.getText().toString();
                if ("".equals(track)) {
                    U.beepError(this, "注文IDは必須です");
                    trackingNum.setFocusableInTouchMode(true);
                    break;
                }

                else {
                    boolean match = false;
                    for(int i =0; i<tracklist.size(); i++)
                    {
                        if(track.equalsIgnoreCase(tracklist.get(i))){
                            match = true;

                            tracklist.remove(i);
                            break;
                        }
                    }
                    if(!match){
                        U.beepError(this, "有効な追跡番号を入力してください");
                        trackingNum.setFocusableInTouchMode(true);
                        _sts(R.id.trackingNumber ,"");
                        break;
                    }
                    else{
                        if(tracklist.size()>0){
                            setProc(PROC_TRACK);
                            totaltrack.setText(tracklist.size()+"");

                        }
                        else
                            GotoNext();
                        _sts(R.id.trackingNumber ,"");
                    }
                }
                break;
        }
    }

    @Override
    public void clearEvent() {

    }

    @Override
    public void allclearEvent() {
        mTextToSpeak.startSpeaking("clear");
        nextProcess();
    }

    @Override
    public void skipEvent() {

    }

    @Override
    public void keypressEvent(String key, String buff) {
        switch (mProcNo) {
            case PROC_ORDER_ID:	// ?????
                _sts(R.id.orderId, buff);
                break;
            case PROC_TRACK:
                _sts(R.id.trackingNumber, buff);
                break;// ?????

        }
    }

    @Override
    public void scanedEvent(String barcode) {
        if (!barcode.equals("")) {

            if (mProcNo == PROC_ORDER_ID) {
                // check for QR code
                _sts(R.id.orderId, barcode);
            }

            if (mProcNo == PROC_TRACK)
                _sts(R.id.trackingNumber, barcode);
        }
        this.inputedEvent(barcode);
    }

    @Override
    public void enterEvent() {

    }

    @Override
    public void deleteEvent(String buff) {
        switch (mProcNo) {
            case PROC_ORDER_ID:	// ?????
                _sts(R.id.orderId, buff);
                break;
            case PROC_TRACK:
                _sts(R.id.trackingNumber, buff);
                break;// ?????

        }
    }

    @Override
    public void nextProcess() {
        this._sts(R.id.orderId, "");
        this._sts(R.id.trackingNumber, "");
        this._stxtv(R.id.totaltrack, "");

        //  this._sts(R.id.countBTN, "");
        this.setProc(PROC_ORDER_ID);
        _gt(R.id.orderId).requestFocus();
        if (showKeyboard == false) {
            visible = false;
            numbrbtn.setText(R.string.showkeyboard);

            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) mainlayout.getLayoutParams();
            params.setMargins(0, 0, 0, 10);
//			Animation bottomDown = AnimationUtils.loadAnimation(this, R.anim.bottom_down);
            ViewGroup hiddenPanel = (ViewGroup) findViewById(R.id.layout_number);
//			hiddenPanel.startAnimation(bottomDown);
            hiddenPanel.setVisibility(View.INVISIBLE);

            mainlayout.setLayoutParams(params);

        }
    }

    public void setProc(int procNo) {
        mProcNo = procNo;
        mBuff.delete(0, mBuff.length());
        switch (procNo) {
            case PROC_ORDER_ID:
                _gt(R.id.orderId).setBackground(getResources().getDrawable(R.drawable.basic_edittext_on));
                _gt(R.id.orderId).setFocusableInTouchMode(true);
                _gt(R.id.trackingNumber).setBackground(getResources().getDrawable(R.drawable.basic_edittext_off));
                break;
            case PROC_TRACK:
                _gt(R.id.trackingNumber).setBackground(getResources().getDrawable(R.drawable.basic_edittext_on));
                _gt(R.id.trackingNumber).setFocusableInTouchMode(true);
                _gt(R.id.orderId).setBackground(getResources().getDrawable(R.drawable.basic_edittext_off));
                break;

        }
    }

    @Override
    public void onClick(View v) {

    }

    public void onSucess(int status, GetShippingOrderInfoResponse message) throws JsonIOException {
        progress.Dismiss();
        mProductList = new ArrayList<>();
        mPackageList = new ArrayList<>();
        tracklist = new ArrayList<>();
        remark = message.getWarehouseRemarks();
        mProductList = message.getProducts();
        mPackageList = message.getPackages();

        for(int i =0; i<mPackageList.size();i++){
            if(mPackageList.get(i).getTrackingNumber()!= null && !mPackageList.get(i).getTrackingNumber().isEmpty()){
                tracklist.add(mPackageList.get(i).getTrackingNumber());
            }
        }
        for(int i =0; i<mProductList.size();i++){
            mProductList.get(i).setScanedQty(0);
        }

        //check do_not_inspect_inquiry_number
        if(!tracklist.isEmpty() && (BaseActivity.getNotInspectTrackingNo() == 0) ) {
            setProc(PROC_TRACK);
            _sts(R.id.trackingNumber ,"");
//            String val = count+"/"+tracklist.size();
            totaltrack.setText(tracklist.size()+"");
        }
        else{
            GotoNext();
        }
    }

    @Override
    public void onSucess(int status, ReshipOrderResponse message) throws JsonIOException {
        progress.Dismiss();
        progress.Show();

        String order = orderId.getText().toString();

        String orderNo;
        if (order.matches("\\d+")) {
            orderNo = "";
        } else {
            orderNo = order;
        }

        Call<GetShippingOrderInfoResponse> apiCall = manager.GetShippingOrderInfo(
                BaseActivity.getShopId(),
                order,
                BaseActivity.get_DeviceID(),
                orderNo,
                BaseActivity.getlotShipmentSelected() ? 1 : 0
        );

        makeApiCall(apiCall, this::onSucess, this::onError,this::onFaliure,this::onNetworkFailure);
    }

    @Override
    public void onError(int status, ResponseBody error) {
        progress.Dismiss();

        try {
            JSONObject jObjError = new JSONObject(error.string());
            String message = jObjError.get("message").toString();
            String code = jObjError.get("error_code").toString();

            if (code.equalsIgnoreCase("E805")) {
                if (BaseActivity.getReRegisterInspection() == 1) {
                    // Show re-inspection dialog
                    reshipdialog = new SweetAlertDialog(OrderActivity.this);
                    reshipdialog.setTitle(getString(R.string.error_order_already_inspected));
                    reshipdialog.setContentText(getString(R.string.error_reinspect_question));
                    reshipdialog.setConfirmButton(getString(R.string.btn_do_reinspect), sweetAlertDialog -> {
                        reshipdialog.dismiss();
                        progress.Show();
                        ReshipOrderRequest request = new ReshipOrderRequest();
                        request.setOrder_id(Integer.parseInt(orderId.getText().toString()));
                        request.setShop_id(BaseActivity.getShopId());
                        manager.ReshipOrderInfo(request, reshipOrderInfoCall);

                    });
                    reshipdialog.setCancelButton(getString(R.string.btn_dont_reinspect), sweetAlertDialog -> {
                        reshipdialog.dismiss();
                    });

                    if (!reshipdialog.isShowing()) {
                        reshipdialog.show();
                    }
                } else {
                    // Order cannot be used if not in printed status
                    U.beepError(this, getString(R.string.error_order_not_in_printed_status));
                }
            } else {
                U.beepError(this, message);
            }
        } catch (JSONException | IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onFaliure() {
        U.beepError(this, "Server Error occured");
    }

    @Override
    public void onNetworkFailure() {
        U.beepError(this, "Network Error occured");
    }
}