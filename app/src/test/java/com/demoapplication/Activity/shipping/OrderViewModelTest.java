package com.demoapplication.Activity.shipping;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.app.Application;

import androidx.arch.core.executor.testing.InstantTaskExecutorRule;

import com.demoapplication.Util.constants.MOrder;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestRule;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class OrderViewModelTest {

    @Rule
    public TestRule rule = new InstantTaskExecutorRule();

    @Mock
    private Application mockApplication;

    private OrderViewModel viewModel;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        viewModel = new OrderViewModel(mockApplication);
    }

    @Test
    public void testInitialState() {
        // Test initial values
        assertEquals("", viewModel.getOrderId().getValue());
        assertEquals("", viewModel.getTrackingNumber().getValue());
        assertEquals(MOrder.ORDER_ID, viewModel.getCurrentProcess().getValue());
        assertFalse(viewModel.getIsLoading().getValue());
    }

    @Test
    public void testHandleKeyboardInput() {
        // Test keyboard input for order ID
        viewModel.handleKeyboardInput("key", "12345");
        assertEquals("12345", viewModel.getOrderId().getValue());
    }

    @Test
    public void testHandleClearEvent() {
        // Set some values first
        viewModel.handleKeyboardInput("key", "12345");
        
        // Clear and verify
        viewModel.handleClearEvent();
        assertEquals("", viewModel.getOrderId().getValue());
    }

    @Test
    public void testHandleAllClearEvent() {
        // Set some values first
        viewModel.handleKeyboardInput("key", "12345");
        
        // Clear all and verify
        viewModel.handleAllClearEvent();
        assertEquals("", viewModel.getOrderId().getValue());
        assertEquals("", viewModel.getTrackingNumber().getValue());
        assertEquals(MOrder.ORDER_ID, viewModel.getCurrentProcess().getValue());
    }
}
