package com.demoapplication.Activity.shipping;

import android.app.Application;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.demoapplication.Activity.BaseActivity;
import com.demoapplication.Modal.Shipping.GetOrder.GetShippingOrderInfoResponse;
import com.demoapplication.Modal.Shipping.ReShip.ReshipOrderRequest;
import com.demoapplication.Modal.Shipping.ReShip.ReshipOrderResponse;
import com.demoapplication.R;
import com.demoapplication.ServerRetro.DataManager;
import com.demoapplication.Util.U;
import com.demoapplication.Util.constants.MOrder;
import com.demoapplication.viewmodel.BaseViewModel;

import java.util.ArrayList;
import java.util.List;

import okhttp3.ResponseBody;

public class OrderViewModel extends BaseViewModel {
    private static final String TAG = "OrderViewModel";

    private final DataManager dataManager;

    // LiveData for UI state
    private final MutableLiveData<String> orderId = new MutableLiveData<>("");
    private final MutableLiveData<String> trackingNumber = new MutableLiveData<>("");
    private final MutableLiveData<String> totalTrackingCount = new MutableLiveData<>("");
    private final MutableLiveData<MOrder> currentProcess = new MutableLiveData<>(MOrder.ORDER_ID);
    private final MutableLiveData<Boolean> orderIdLayoutVisible = new MutableLiveData<>(true);
    private final MutableLiveData<Boolean> trackingLayoutVisible = new MutableLiveData<>(true);
    private final MutableLiveData<Integer> scrollPosition = new MutableLiveData<>();

    // Data for business logic
    private final MutableLiveData<GetShippingOrderInfoResponse> orderInfoResponse = new MutableLiveData<>();
    private final MutableLiveData<List<GetShippingOrderInfoResponse.ProductData>> productList = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<List<GetShippingOrderInfoResponse.PackagesData>> packageList = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<List<String>> trackingList = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<String> warehouseRemarks = new MutableLiveData<>("");

    // Navigation events
    private final MutableLiveData<Boolean> shouldNavigateToShipping = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> shouldShowReshipDialog = new MutableLiveData<>(false);

    public OrderViewModel(Application application) {
        super(application);
        dataManager = new DataManager(application.getApplicationContext());
        initializeVisibility();
    }

    private void initializeVisibility() {
        if (BaseActivity.getShippingStartKey() == 0) {
            orderIdLayoutVisible.setValue(true);
            if (BaseActivity.getNotInspectTrackingNo() == 0) {
                trackingLayoutVisible.setValue(true);
            } else {
                trackingLayoutVisible.setValue(false);
            }
            currentProcess.setValue(MOrder.ORDER_ID);
        } else {
            orderIdLayoutVisible.setValue(false);
            if (BaseActivity.getNotInspectTrackingNo() == 0) {
                trackingLayoutVisible.setValue(true);
                currentProcess.setValue(MOrder.TRACKING_NUMBER);
            } else {
                trackingLayoutVisible.setValue(false);
                message.setValue("(HMS Parameter error) Tracking number is not show");
            }
        }
    }

    // Getters for LiveData
    public LiveData<String> getOrderId() { return orderId; }
    public LiveData<String> getTrackingNumber() { return trackingNumber; }
    public LiveData<String> getTotalTrackingCount() { return totalTrackingCount; }
    public LiveData<MOrder> getCurrentProcess() { return currentProcess; }
    public LiveData<Boolean> getOrderIdLayoutVisible() { return orderIdLayoutVisible; }
    public LiveData<Boolean> getTrackingLayoutVisible() { return trackingLayoutVisible; }
    public LiveData<Integer> getScrollPosition() { return scrollPosition; }
    public LiveData<GetShippingOrderInfoResponse> getOrderInfoResponse() { return orderInfoResponse; }
    public LiveData<List<GetShippingOrderInfoResponse.ProductData>> getProductList() { return productList; }
    public LiveData<List<GetShippingOrderInfoResponse.PackagesData>> getPackageList() { return packageList; }
    public LiveData<List<String>> getTrackingList() { return trackingList; }
    public LiveData<String> getWarehouseRemarks() { return warehouseRemarks; }
    public LiveData<Boolean> getShouldNavigateToShipping() { return shouldNavigateToShipping; }
    public LiveData<Boolean> getShouldShowReshipDialog() { return shouldShowReshipDialog; }

    // Input handling methods
    public void handleInputEvent(String input, boolean isScanned) {
        MOrder process = currentProcess.getValue();
        if (process == null) return;

        switch (process) {
            case ORDER_ID:
                handleOrderIdInput(input);
                break;
            case TRACKING_NUMBER:
                handleTrackingNumberInput(input);
                break;
        }
    }

    private void handleOrderIdInput(String orderIdInput) {
        if (orderIdInput.isEmpty()) {
            handleApiError("error_order_id_required", message, isProcessValid);
            return;
        }

        orderId.setValue(orderIdInput);
        fetchOrderInfo(orderIdInput);
    }

    private void handleTrackingNumberInput(String trackingInput) {
        if (trackingInput.isEmpty()) {
            handleApiError("error_tracking_number_required", message, isProcessValid);
            return;
        }

        List<String> currentTrackingList = trackingList.getValue();
        if (currentTrackingList == null) return;

        boolean match = false;
        for (int i = 0; i < currentTrackingList.size(); i++) {
            if (trackingInput.equalsIgnoreCase(currentTrackingList.get(i))) {
                match = true;
                currentTrackingList.remove(i);
                break;
            }
        }

        if (!match) {
            handleApiError("error_invalid_tracking_number", message, isProcessValid);
            trackingNumber.setValue("");
            return;
        }

        trackingList.setValue(currentTrackingList);

        if (currentTrackingList.size() > 0) {
            currentProcess.setValue(MOrder.TRACKING_NUMBER);
            totalTrackingCount.setValue(String.valueOf(currentTrackingList.size()));
            trackingNumber.setValue("");
        } else {
            // All tracking numbers processed, navigate to shipping
            shouldNavigateToShipping.setValue(true);
        }
    }

    public void handleKeyboardInput(String key, String buff) {
        MOrder process = currentProcess.getValue();
        if (process == null) return;

        switch (process) {
            case ORDER_ID:
                orderId.setValue(buff);
                break;
            case TRACKING_NUMBER:
                trackingNumber.setValue(buff);
                break;
        }
    }

    public void handleDeleteEvent(String currentValue) {
        MOrder process = currentProcess.getValue();
        if (process == null) return;

        if (currentValue.length() > 0) {
            String newValue = currentValue.substring(0, currentValue.length() - 1);
            switch (process) {
                case ORDER_ID:
                    orderId.setValue(newValue);
                    break;
                case TRACKING_NUMBER:
                    trackingNumber.setValue(newValue);
                    break;
            }
        }
    }

    public void handleClearEvent() {
        MOrder process = currentProcess.getValue();
        if (process == null) return;

        switch (process) {
            case ORDER_ID:
                orderId.setValue("");
                break;
            case TRACKING_NUMBER:
                trackingNumber.setValue("");
                break;
        }
    }

    public void handleAllClearEvent() {
        orderId.setValue("");
        trackingNumber.setValue("");
        totalTrackingCount.setValue("");
        productList.setValue(new ArrayList<>());
        packageList.setValue(new ArrayList<>());
        trackingList.setValue(new ArrayList<>());
        warehouseRemarks.setValue("");
        currentProcess.setValue(MOrder.ORDER_ID);
        message.setValue("");
        isProcessValid.setValue(false);
        setScrollPosition(0);
    }

    public void setScrollPosition(int position) {
        scrollPosition.setValue(position);
    }

    // API calls
    private void fetchOrderInfo(String orderIdInput) {
        isLoading.setValue(true);

        String orderNo = orderIdInput.matches("\\d+") ? "" : orderIdInput;

        makeApiCall(dataManager.GetShippingOrderInfo(
                BaseActivity.getShopId(),
                orderIdInput,
                BaseActivity.get_DeviceID(),
                orderNo,
                BaseActivity.getlotShipmentSelected() ? 1 : 0
        ), this::handleOrderInfoSuccess, this::handleOrderInfoError, this::handleApiFailure, this::handleNetworkFailure);
    }

    public void reshipOrder() {
        isLoading.setValue(true);
        String orderIdValue = orderId.getValue();
        if (orderIdValue == null || orderIdValue.isEmpty()) return;

        String orderNo = orderIdValue.matches("\\d+") ? "" : orderIdValue;
        int orderIdInt;
        try {
            orderIdInt = Integer.parseInt(orderIdValue);
        } catch (NumberFormatException e) {
            orderIdInt = 0;
        }

        ReshipOrderRequest request = new ReshipOrderRequest(orderIdInt, orderNo, BaseActivity.getShopId());

        makeApiCall(dataManager.ReshipOrderInfo(request),
                this::handleReshipSuccess,
                this::handleOrderInfoError,
                this::handleApiFailure,
                this::handleNetworkFailure);
    }

    // API response handlers
    private void handleOrderInfoSuccess(int status, GetShippingOrderInfoResponse response) {
        isLoading.setValue(false);

        if (response == null) return;

        orderInfoResponse.setValue(response);
        warehouseRemarks.setValue(response.getWarehouseRemarks());

        ArrayList<GetShippingOrderInfoResponse.ProductData> products = response.getProducts();
        ArrayList<GetShippingOrderInfoResponse.PackagesData> packages = response.getPackages();

        if (products != null) {
            for (GetShippingOrderInfoResponse.ProductData product : products) {
                product.setScanedQty(0);
            }
            productList.setValue(products);
        }

        if (packages != null) {
            packageList.setValue(packages);

            // Extract tracking numbers
            List<String> trackingNumbers = new ArrayList<>();
            for (GetShippingOrderInfoResponse.PackagesData packageData : packages) {
                if (packageData.getTrackingNumber() != null && !packageData.getTrackingNumber().isEmpty()) {
                    trackingNumbers.add(packageData.getTrackingNumber());
                }
            }
            trackingList.setValue(trackingNumbers);

            // Check if we need to process tracking numbers
            if (!trackingNumbers.isEmpty() && BaseActivity.getNotInspectTrackingNo() == 0) {
                currentProcess.setValue(MOrder.TRACKING_NUMBER);
                trackingNumber.setValue("");
                totalTrackingCount.setValue(String.valueOf(trackingNumbers.size()));
            } else {
                // No tracking numbers to process, navigate directly to shipping
                shouldNavigateToShipping.setValue(true);
            }
        }
    }

    private void handleReshipSuccess(int status, ReshipOrderResponse response) {
        isLoading.setValue(false);
        // After successful reship, fetch order info again
        String orderIdValue = orderId.getValue();
        if (orderIdValue != null && !orderIdValue.isEmpty()) {
            fetchOrderInfo(orderIdValue);
        }
    }

    private void handleOrderInfoError(int status, ResponseBody error) {
        isLoading.setValue(false);

        try {
            String errorMessage = error.string();
            Log.e(TAG, "API Error: " + errorMessage);

            // Check if this is a reship scenario (status 402)
            if (status == 402) {
                shouldShowReshipDialog.setValue(true);
            } else {
                handleApiError("error_api_call", message, isProcessValid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            handleApiError("error_api_call", message, isProcessValid);
        }
    }

    private void handleApiFailure() {
        isLoading.setValue(false);
        message.setValue("Server Error occurred");
    }

    private void handleNetworkFailure() {
        isLoading.setValue(false);
        message.setValue("Network Error occurred");
    }

    // Helper method to handle API errors
    private void handleApiError(String errorKey, MutableLiveData<String> messageLiveData, MutableLiveData<Boolean> validLiveData) {
        // You can implement localization here if needed
        switch (errorKey) {
            case "error_order_id_required":
                messageLiveData.setValue("注文IDは必須です");
                break;
            case "error_tracking_number_required":
                messageLiveData.setValue("追跡番号は必須です");
                break;
            case "error_invalid_tracking_number":
                messageLiveData.setValue("有効な追跡番号を入力してください");
                break;
            case "error_api_call":
                messageLiveData.setValue("API call failed");
                break;
            case "error_server":
                messageLiveData.setValue("Server Error occurred");
                break;
            case "error_network_connection":
                messageLiveData.setValue("Network Error occurred");
                break;
            default:
                messageLiveData.setValue("Unknown error occurred");
                break;
        }
        if (validLiveData != null) {
            validLiveData.setValue(false);
        }
    }

    // Navigation events reset
    public void onNavigationHandled() {
        shouldNavigateToShipping.setValue(false);
    }

    public void onReshipDialogHandled() {
        shouldShowReshipDialog.setValue(false);
    }
}
